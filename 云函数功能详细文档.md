# 三角洲小程序云函数功能详细文档

## 📋 目录

- [用户相关云函数](#用户相关云函数)
- [订单相关云函数](#订单相关云函数)
- [聊天相关云函数](#聊天相关云函数)
- [通知相关云函数](#通知相关云函数)
- [支付相关云函数](#支付相关云函数)
- [评价相关云函数](#评价相关云函数)
- [系统相关云函数](#系统相关云函数)
- [管理后台云函数](#管理后台云函数)
- [工具类云函数](#工具类云函数)

---

## 用户相关云函数

### 1. login (用户登录)
**文件路径**: `cloudfunctions/login/index.js`
**功能描述**: 处理用户微信登录，创建或更新用户信息

**输入参数**:
```javascript
{
  nickName: String,    // 微信昵称
  avatarUrl: String,   // 微信头像URL
  gender: Number       // 性别 (0-未知, 1-男, 2-女)
}
```

**返回值**:
```javascript
{
  success: Boolean,
  data: {
    userInfo: Object,    // 用户信息
    isNewUser: Boolean   // 是否为新用户
  }
}
```

**主要功能**:
- 通过微信openid识别用户
- 新用户自动注册，老用户更新信息
- 初始化用户钱包余额和信誉分数

---

### 2. getUserInfo (获取用户信息)
**文件路径**: `cloudfunctions/getUserInfo/index.js`
**功能描述**: 获取当前用户的详细信息和统计数据

**输入参数**: 无 (通过openid自动识别用户)

**返回值**:
```javascript
{
  success: Boolean,
  data: {
    userInfo: Object,    // 用户基本信息
    userStats: {         // 用户统计数据
      totalOrders: Number,      // 总订单数
      completedOrders: Number,  // 完成订单数
      rating: Number,           // 平均评分
      balance: Number,          // 钱包余额
      creditScore: Number       // 信誉分数
    }
  }
}
```

**主要功能**:
- 获取用户基本信息
- 计算用户订单统计
- 获取用户评价和信誉数据

---

### 3. updateUserInfo (更新用户信息)
**文件路径**: `cloudfunctions/updateUserInfo/index.js`
**功能描述**: 更新用户个人信息

**输入参数**:
```javascript
{
  nickName: String,        // 昵称
  gameNickName: String,    // 游戏昵称
  phone: String,           // 手机号
  bio: String,             // 个人简介
  contactInfo: Object,     // 联系方式
  settings: Object         // 用户设置
}
```

**返回值**:
```javascript
{
  success: Boolean,
  data: Object,    // 更新后的用户信息
  message: String
}
```

**主要功能**:
- 更新用户基本信息
- 更新最后活跃时间
- 数据验证和安全检查

---

### 4. getUserDetail (获取用户详情)
**文件路径**: `cloudfunctions/getUserDetail/index.js`
**功能描述**: 获取指定用户的详细信息

**输入参数**:
```javascript
{
  userId: String    // 用户ID
}
```

**返回值**:
```javascript
{
  success: Boolean,
  data: {
    _id: String,
    nickname: String,
    avatar: String,
    phone: String,
    isVerified: Boolean,
    status: String,
    balance: Number,
    creditScore: Number,
    orderCount: Number
    // ... 其他用户信息
  }
}
```

**主要功能**:
- 获取指定用户的公开信息
- 用于订单详情页显示用户信息
- 隐私信息保护

---

### 5. certification (实名认证)
**文件路径**: `cloudfunctions/certification/index.js`
**功能描述**: 处理用户实名认证

**输入参数**:
```javascript
{
  realName: String,     // 真实姓名
  idCard: String,       // 身份证号
  frontImage: String,   // 身份证正面照
  backImage: String     // 身份证背面照
}
```

**返回值**:
```javascript
{
  success: Boolean,
  data: {
    certificationId: String,
    status: String    // pending, approved, rejected
  }
}
```

**主要功能**:
- 提交实名认证申请
- 身份证信息验证
- 认证状态管理

---

### 6. bindPhone (绑定手机号)
**文件路径**: `cloudfunctions/bindPhone/index.js`
**功能描述**: 绑定用户手机号

**输入参数**:
```javascript
{
  encryptedData: String,    // 加密数据
  iv: String                // 初始向量
}
```

**返回值**:
```javascript
{
  success: Boolean,
  data: {
    phone: String    // 解密后的手机号
  }
}
```

**主要功能**:
- 解密微信手机号数据
- 绑定手机号到用户账户
- 验证手机号唯一性

---

## 订单相关云函数

### 7. createOrder (创建订单)
**文件路径**: `cloudfunctions/createOrder/index.js`
**功能描述**: 创建新的游戏陪玩订单

**输入参数**:
```javascript
{
  title: String,           // 订单标题
  content: String,         // 任务内容描述
  reward: Number,          // 悬赏金额
  platformType: String,    // 平台类型 (mobile/pc)
  serviceType: String,     // 服务类型 (duration/rounds)
  duration: Number,        // 时长 (小时)
  rounds: Number,          // 局数
  tags: Array,            // 标签数组
  orderType: String,      // 订单类型 (immediate/scheduled)
  scheduledDate: String,  // 预约日期
  scheduledTime: String   // 预约时间
}
```

**返回值**:
```javascript
{
  success: Boolean,
  data: {
    orderId: String,    // 订单ID
    orderNo: String     // 订单编号
  }
}
```

**主要功能**:
- 创建新订单记录
- 生成唯一订单编号
- 支持即时和预约两种类型
- 自动计算平台费用分成

---

### 8. getOrderList (获取订单列表)
**文件路径**: `cloudfunctions/getOrderList/index.js`
**功能描述**: 获取用户的订单列表

**输入参数**:
```javascript
{
  page: Number,        // 页码
  pageSize: Number,    // 每页数量
  status: String,      // 订单状态筛选
  role: String         // 用户角色 (customer/accepter)
}
```

**返回值**:
```javascript
{
  success: Boolean,
  data: {
    list: Array,         // 订单列表
    total: Number,       // 总数量
    hasMore: Boolean     // 是否有更多数据
  }
}
```

**主要功能**:
- 分页获取订单列表
- 按状态和角色筛选
- 包含订单基本信息和状态

---

### 9. getOrderDetail (获取订单详情)
**文件路径**: `cloudfunctions/getOrderDetail/index.js`
**功能描述**: 获取订单的详细信息

**输入参数**:
```javascript
{
  orderId: String    // 订单ID
}
```

**返回值**:
```javascript
{
  success: Boolean,
  data: {
    orderInfo: Object,      // 订单详细信息
    customerInfo: Object,   // 客户信息
    accepterInfo: Object,   // 接单者信息
    chatRoomId: String,     // 聊天室ID
    canOperate: Boolean     // 是否可操作
  }
}
```

**主要功能**:
- 获取订单完整信息
- 包含相关用户信息
- 权限验证和操作判断

---

### 10. getGrabOrderList (获取抢单大厅列表)
**文件路径**: `cloudfunctions/getGrabOrderList/index.js`
**功能描述**: 获取抢单大厅的可接订单列表

**输入参数**:
```javascript
{
  page: Number,        // 页码
  pageSize: Number     // 每页数量
}
```

**返回值**:
```javascript
{
  success: Boolean,
  data: {
    list: Array,         // 可抢订单列表
    total: Number,       // 总数量
    hasMore: Boolean     // 是否有更多数据
  }
}
```

**主要功能**:
- 获取状态为pending的订单
- 排除用户自己发布的订单
- 按创建时间倒序排列

---

### 11. acceptOrder (接单/抢单)
**文件路径**: `cloudfunctions/acceptOrder/index.js`
**功能描述**: 用户接单或抢单功能

**输入参数**:
```javascript
{
  orderId: String    // 订单ID
}
```

**返回值**:
```javascript
{
  success: Boolean,
  data: {
    orderId: String,
    status: String,
    chatRoomId: String
  }
}
```

**主要功能**:
- 原子性抢单操作
- 并发控制和重试机制
- 自动创建聊天室
- 发送通知给客户

---

### 12. updateOrderStatus (更新订单状态)
**文件路径**: `cloudfunctions/updateOrderStatus/index.js`
**功能描述**: 更新订单状态

**输入参数**:
```javascript
{
  orderId: String,     // 订单ID
  status: String,      // 新状态
  reason: String       // 状态变更原因
}
```

**返回值**:
```javascript
{
  success: Boolean,
  data: {
    orderId: String,
    status: String
  }
}
```

**主要功能**:
- 订单状态流转管理
- 权限验证
- 状态变更通知

---

### 13. cancelOrder (取消订单)
**文件路径**: `cloudfunctions/cancelOrder/index.js`
**功能描述**: 取消订单功能

**输入参数**:
```javascript
{
  orderId: String,     // 订单ID
  reason: String,      // 取消原因
  userRole: String     // 用户角色
}
```

**返回值**:
```javascript
{
  success: Boolean,
  data: {
    orderId: String,
    status: String
  }
}
```

**主要功能**:
- 订单取消处理
- 权限验证
- 聊天室状态更新
- 实时通知发送

---

### 14. updateOrder (更新订单)
**文件路径**: `cloudfunctions/updateOrder/index.js`
**功能描述**: 更新订单信息

**输入参数**:
```javascript
{
  orderId: String,     // 订单ID
  updateData: Object   // 更新数据
}
```

**返回值**:
```javascript
{
  success: Boolean,
  data: Object    // 更新后的订单信息
}
```

**主要功能**:
- 订单信息修改
- 数据验证
- 权限检查

---

## 聊天相关云函数

### 15. chatRoom (聊天室管理)
**文件路径**: `cloudfunctions/chatRoom/index.js`
**功能描述**: 聊天室创建和管理

**输入参数**:
```javascript
{
  action: String,      // 操作类型
  orderNo: String,     // 订单编号
  orderId: String      // 订单ID
}
```

**返回值**:
```javascript
{
  success: Boolean,
  data: {
    chatRoomId: String,
    orderInfo: Object,
    participants: Array
  }
}
```

**主要功能**:
- 基于订单自动创建聊天室
- 聊天室信息管理
- 参与者权限控制

---

### 16. chatMessage (聊天消息)
**文件路径**: `cloudfunctions/chatMessage/index.js`
**功能描述**: 聊天消息发送和管理

**输入参数**:
```javascript
{
  action: String,        // 操作类型
  chatRoomId: String,    // 聊天室ID
  content: String,       // 消息内容
  messageType: String    // 消息类型
}
```

**返回值**:
```javascript
{
  success: Boolean,
  data: {
    messageId: String,
    message: Object
  }
}
```

**主要功能**:
- 发送文本、图片、语音消息
- 消息状态管理
- 实时消息推送

---

### 17. chatList (聊天列表)
**文件路径**: `cloudfunctions/chatList/index.js`
**功能描述**: 获取用户的聊天列表

**输入参数**:
```javascript
{
  page: Number,        // 页码
  pageSize: Number,    // 每页数量
  status: String       // 聊天室状态
}
```

**返回值**:
```javascript
{
  success: Boolean,
  data: {
    list: Array,         // 聊天室列表
    stats: Object        // 统计信息
  }
}
```

**主要功能**:
- 获取用户参与的聊天室
- 显示最后消息和时间
- 未读消息统计

---

### 18. updateChatRoomLastMessage (更新最后消息)
**文件路径**: `cloudfunctions/updateChatRoomLastMessage/index.js`
**功能描述**: 更新聊天室的最后消息信息

**输入参数**:
```javascript
{
  chatRoomId: String,    // 聊天室ID
  lastMessage: Object    // 最后消息对象
}
```

**返回值**:
```javascript
{
  success: Boolean,
  data: Object
}
```

**主要功能**:
- 更新聊天室最后消息
- 维护聊天列表排序
- 消息时间戳管理

---

## 通知相关云函数

### 19. sendNotification (发送通知)
**文件路径**: `cloudfunctions/sendNotification/index.js`
**功能描述**: 发送系统通知

**输入参数**:
```javascript
{
  userId: String,        // 接收用户ID
  type: String,          // 通知类型
  title: String,         // 通知标题
  content: String,       // 通知内容
  relatedId: String      // 关联ID
}
```

**返回值**:
```javascript
{
  success: Boolean,
  data: {
    notificationId: String
  }
}
```

**主要功能**:
- 发送各类系统通知
- 支持订阅消息推送
- 通知优先级管理

---

### 20. getNotificationList (获取通知列表)
**文件路径**: `cloudfunctions/getNotificationList/index.js`
**功能描述**: 获取用户的通知列表

**输入参数**:
```javascript
{
  page: Number,          // 页码
  pageSize: Number,      // 每页数量
  type: String,          // 通知类型
  onlyUnread: Boolean    // 只显示未读
}
```

**返回值**:
```javascript
{
  success: Boolean,
  data: {
    list: Array,         // 通知列表
    unreadCount: Number  // 未读数量
  }
}
```

**主要功能**:
- 分页获取通知列表
- 按类型筛选通知
- 未读通知统计

---

### 21. markNotificationRead (标记通知已读)
**文件路径**: `cloudfunctions/markNotificationRead/index.js`
**功能描述**: 标记通知为已读状态

**输入参数**:
```javascript
{
  notificationId: String    // 通知ID
}
```

**返回值**:
```javascript
{
  success: Boolean,
  data: Object
}
```

**主要功能**:
- 单个通知标记已读
- 批量标记已读
- 已读时间记录

---

### 22. notificationManager (通知管理器)
**文件路径**: `cloudfunctions/notificationManager/index.js`
**功能描述**: 通知系统管理功能

**输入参数**:
```javascript
{
  action: String,    // 操作类型
  data: Object       // 操作数据
}
```

**返回值**:
```javascript
{
  success: Boolean,
  data: Object
}
```

**主要功能**:
- 通知模板管理
- 批量通知发送
- 通知统计分析

---

## 支付相关云函数

### 23. createRecharge (创建充值)
**文件路径**: `cloudfunctions/createRecharge/index.js`
**功能描述**: 创建充值订单

**输入参数**:
```javascript
{
  amount: Number,      // 充值金额
  paymentMethod: String // 支付方式
}
```

**返回值**:
```javascript
{
  success: Boolean,
  data: {
    rechargeId: String,
    paymentInfo: Object
  }
}
```

**主要功能**:
- 创建充值记录
- 生成支付参数
- 支付状态跟踪

---

### 24. createWithdraw (创建提现)
**文件路径**: `cloudfunctions/createWithdraw/index.js`
**功能描述**: 创建提现申请

**输入参数**:
```javascript
{
  amount: Number,        // 提现金额
  withdrawMethod: String, // 提现方式
  accountInfo: Object    // 账户信息
}
```

**返回值**:
```javascript
{
  success: Boolean,
  data: {
    withdrawId: String,
    status: String
  }
}
```

**主要功能**:
- 创建提现申请
- 余额验证
- 提现审核流程

---

### 25. getTransactionList (获取交易记录)
**文件路径**: `cloudfunctions/getTransactionList/index.js`
**功能描述**: 获取用户交易记录

**输入参数**:
```javascript
{
  page: Number,        // 页码
  pageSize: Number,    // 每页数量
  type: String         // 交易类型
}
```

**返回值**:
```javascript
{
  success: Boolean,
  data: {
    list: Array,         // 交易记录列表
    total: Number        // 总数量
  }
}
```

**主要功能**:
- 获取充值提现记录
- 订单收支记录
- 交易统计信息

---

### 26. getTransactionRecords (获取交易记录详情)
**文件路径**: `cloudfunctions/getTransactionRecords/index.js`
**功能描述**: 获取详细的交易记录

**输入参数**:
```javascript
{
  transactionId: String    // 交易ID
}
```

**返回值**:
```javascript
{
  success: Boolean,
  data: Object    // 交易详情
}
```

**主要功能**:
- 获取交易详细信息
- 交易状态查询
- 相关订单信息

---

### 27. paymentCallback (支付回调)
**文件路径**: `cloudfunctions/paymentCallback/index.js`
**功能描述**: 处理支付回调

**输入参数**:
```javascript
{
  paymentData: Object    // 支付平台回调数据
}
```

**返回值**:
```javascript
{
  success: Boolean,
  data: Object
}
```

**主要功能**:
- 处理支付成功回调
- 更新订单状态
- 余额变更记录

---

## 评价相关云函数

### 28. submitEvaluation (提交评价)
**文件路径**: `cloudfunctions/submitEvaluation/index.js`
**功能描述**: 提交订单评价

**输入参数**:
```javascript
{
  orderId: String,       // 订单ID
  rating: Number,        // 评分 (1-5)
  comment: String,       // 评价内容
  tags: Array           // 评价标签
}
```

**返回值**:
```javascript
{
  success: Boolean,
  data: {
    evaluationId: String
  }
}
```

**主要功能**:
- 提交订单评价
- 更新用户信誉分数
- 评价数据统计

---

### 29. getEvaluationInfo (获取评价信息)
**文件路径**: `cloudfunctions/getEvaluationInfo/index.js`
**功能描述**: 获取评价详情

**输入参数**:
```javascript
{
  orderId: String    // 订单ID
}
```

**返回值**:
```javascript
{
  success: Boolean,
  data: {
    customerEvaluation: Object,  // 客户评价
    accepterEvaluation: Object   // 接单者评价
  }
}
```

**主要功能**:
- 获取双向评价信息
- 评价展示格式化
- 评价统计数据

---

### 30. getUserEvaluationStats (获取用户评价统计)
**文件路径**: `cloudfunctions/getUserEvaluationStats/index.js`
**功能描述**: 获取用户评价统计信息

**输入参数**:
```javascript
{
  userId: String    // 用户ID
}
```

**返回值**:
```javascript
{
  success: Boolean,
  data: {
    averageRating: Number,    // 平均评分
    totalEvaluations: Number, // 总评价数
    ratingDistribution: Object // 评分分布
  }
}
```

**主要功能**:
- 计算用户平均评分
- 评价数量统计
- 评分分布分析

---

## 系统相关云函数

### 31. getDailyPassword (获取每日密码)
**文件路径**: `cloudfunctions/getDailyPassword/index.js`
**功能描述**: 获取游戏每日密码

**输入参数**: 无

**返回值**:
```javascript
{
  success: Boolean,
  data: {
    date: String,           // 日期
    locations: Array,       // 各地点密码
    updateTime: Date        // 更新时间
  }
}
```

**主要功能**:
- 从数据库获取缓存的每日密码
- 支持中国时区时间处理
- 密码数据格式化

---

### 32. scheduledPasswordUpdate (定时密码更新)
**文件路径**: `cloudfunctions/scheduledPasswordUpdate/index.js`
**功能描述**: 定时获取并更新每日密码

**触发方式**: 定时触发 (每日凌晨12:00)

**主要功能**:
- 从外部网站爬取最新密码
- 解析各地点密码信息
- 保存到数据库缓存
- 支持自动重试机制

---

### 33. initDatabase (初始化数据库)
**文件路径**: `cloudfunctions/initDatabase/index.js`
**功能描述**: 初始化数据库集合和索引

**主要功能**:
- 创建必要的数据库集合
- 设置数据库索引
- 初始化基础数据

---

### 34. reputationSystem (信誉系统)
**文件路径**: `cloudfunctions/reputationSystem/index.js`
**功能描述**: 用户信誉系统管理

**输入参数**:
```javascript
{
  action: String,    // 操作类型
  data: Object       // 操作数据
}
```

**支持操作**:
- getUserReputation: 获取用户信誉
- updateReputation: 更新信誉分数
- getReputationHistory: 获取信誉历史
- getLeaderboard: 获取排行榜

**主要功能**:
- 信誉分数计算
- 等级系统管理
- 徽章系统
- 排行榜功能

---

### 35. securityMiddleware (安全中间件)
**文件路径**: `cloudfunctions/securityMiddleware/index.js`
**功能描述**: 安全检查和防护

**主要功能**:
- 请求频率限制
- 恶意行为检测
- 数据安全验证
- 访问日志记录

---

## 管理后台云函数

### 36. adminApi (管理后台API)
**文件路径**: `cloudfunctions/adminApi/index.js`
**功能描述**: 管理后台统一API接口

**支持功能**:
- 仪表盘数据统计
- 用户管理
- 订单管理
- 钱包管理
- 系统设置

**主要接口**:
- getDashboardStats: 获取仪表盘统计
- getUserList: 获取用户列表
- getOrderList: 获取订单列表
- getTransactionList: 获取交易列表
- adminLogin: 管理员登录

---

## 工具类云函数

### 37. common/timeZoneUtils (时区工具)
**文件路径**: `cloudfunctions/common/timeZoneUtils.js`
**功能描述**: 统一的时区处理工具

**主要方法**:
- getChinaTime(): 获取中国时间
- getTodayStartUTC(): 获取今日开始UTC时间
- createStorageTime(): 创建存储时间
- getChinaDateString(): 获取中国日期字符串

---

### 38. fix-users (用户数据修复)
**文件路径**: `cloudfunctions/fix-users/index.js`
**功能描述**: 修复用户数据问题

**主要功能**:
- 修复缺失的用户字段
- 数据格式标准化
- 历史数据迁移

---

### 39. cleanExpiredLocks (清理过期锁)
**文件路径**: `cloudfunctions/cleanExpiredLocks/index.js`
**功能描述**: 清理过期的分布式锁

**主要功能**:
- 定时清理过期锁记录
- 防止死锁问题
- 系统性能优化

---

### 40. optimizeDatabase (数据库优化)
**文件路径**: `cloudfunctions/optimizeDatabase/index.js`
**功能描述**: 数据库性能优化

**主要功能**:
- 清理过期数据
- 重建索引
- 数据库统计更新

---

## 📝 使用说明

### 调用方式
```javascript
// 小程序端调用示例
wx.cloud.callFunction({
  name: 'functionName',
  data: {
    // 参数
  },
  success: res => {
    console.log('调用成功', res.result);
  },
  fail: err => {
    console.error('调用失败', err);
  }
});
```

### 错误处理
所有云函数都遵循统一的错误处理格式：
```javascript
{
  success: false,
  error: "错误信息",
  code: "错误代码" // 可选
}
```

### 权限验证
- 大部分云函数通过 `wxContext.OPENID` 自动识别用户
- 管理后台相关函数需要管理员权限验证
- 敏感操作需要额外的权限检查

---

## 📊 统计相关云函数

### 41. getOrderStatistics (获取订单统计)
**文件路径**: `cloudfunctions/getOrderStatistics/index.js`
**功能描述**: 获取订单统计数据

**输入参数**:
```javascript
{
  timeRange: String,    // 时间范围 (today/week/month/year)
  userId: String        // 用户ID (可选)
}
```

**返回值**:
```javascript
{
  success: Boolean,
  data: {
    totalOrders: Number,      // 总订单数
    completedOrders: Number,  // 完成订单数
    cancelledOrders: Number,  // 取消订单数
    totalRevenue: Number,     // 总收入
    averageRating: Number     // 平均评分
  }
}
```

**主要功能**:
- 按时间范围统计订单数据
- 支持用户维度和全局统计
- 收入和评分统计

---

### 42. getOrderCounts (获取订单数量)
**文件路径**: `cloudfunctions/getOrderCounts/index.js`
**功能描述**: 获取各状态订单数量

**输入参数**:
```javascript
{
  userId: String    // 用户ID (可选)
}
```

**返回值**:
```javascript
{
  success: Boolean,
  data: {
    pending: Number,     // 待接单
    accepted: Number,    // 已接单
    completed: Number,   // 已完成
    cancelled: Number    // 已取消
  }
}
```

**主要功能**:
- 实时统计各状态订单数量
- 支持用户个人统计
- 用于仪表盘展示

---

### 43. getWalletStats (获取钱包统计)
**文件路径**: `cloudfunctions/getWalletStats/index.js`
**功能描述**: 获取钱包统计信息

**输入参数**:
```javascript
{
  userId: String,       // 用户ID
  timeRange: String     // 时间范围
}
```

**返回值**:
```javascript
{
  success: Boolean,
  data: {
    currentBalance: Number,    // 当前余额
    totalIncome: Number,       // 总收入
    totalExpense: Number,      // 总支出
    recentTransactions: Array  // 最近交易
  }
}
```

**主要功能**:
- 钱包余额统计
- 收支明细分析
- 交易趋势统计

---

## 🔧 公告相关云函数

### 44. getAnnouncementList (获取公告列表)
**文件路径**: `cloudfunctions/getAnnouncementList/index.js`
**功能描述**: 获取系统公告列表

**输入参数**:
```javascript
{
  page: Number,         // 页码
  pageSize: Number,     // 每页数量
  type: String,         // 公告类型
  status: String,       // 公告状态
  isAdmin: Boolean      // 是否管理员
}
```

**返回值**:
```javascript
{
  success: Boolean,
  data: {
    list: Array,          // 公告列表
    total: Number,        // 总数量
    totalPages: Number    // 总页数
  }
}
```

**主要功能**:
- 分页获取公告列表
- 支持类型和状态筛选
- 置顶和优先级排序
- 缓存机制优化性能

---

### 45. announcementManager (公告管理)
**文件路径**: `cloudfunctions/announcementManager/index.js`
**功能描述**: 公告管理功能

**支持操作**:
- create: 创建公告
- update: 更新公告
- delete: 删除公告
- getDetail: 获取公告详情
- updateViewCount: 更新浏览次数
- batchUpdate: 批量更新状态

**输入参数**:
```javascript
{
  action: String,           // 操作类型
  announcementId: String,   // 公告ID
  data: Object             // 操作数据
}
```

**主要功能**:
- 公告CRUD操作
- 生效时间管理
- 浏览统计
- 批量操作支持

---

## 🔐 认证相关云函数

### 46. adminLogin (管理员登录)
**文件路径**: `cloudfunctions/adminLogin/index.js`
**功能描述**: 管理员登录验证

**输入参数**:
```javascript
{
  username: String,    // 用户名
  password: String     // 密码
}
```

**返回值**:
```javascript
{
  success: Boolean,
  data: {
    token: String,       // 访问令牌
    userInfo: Object,    // 管理员信息
    permissions: Array   // 权限列表
  }
}
```

**主要功能**:
- 管理员身份验证
- JWT令牌生成
- 权限信息返回
- 登录日志记录

---

### 47. verifyToken (验证令牌)
**文件路径**: `cloudfunctions/verifyToken/index.js`
**功能描述**: 验证访问令牌

**输入参数**:
```javascript
{
  token: String    // 访问令牌
}
```

**返回值**:
```javascript
{
  success: Boolean,
  data: {
    valid: Boolean,      // 令牌是否有效
    userInfo: Object,    // 用户信息
    expiresAt: Date      // 过期时间
  }
}
```

**主要功能**:
- JWT令牌验证
- 令牌过期检查
- 用户信息解析
- 权限验证

---

## 📱 小程序特定云函数

### 48. initNotifications (初始化通知)
**文件路径**: `cloudfunctions/initNotifications/index.js`
**功能描述**: 初始化通知系统

**主要功能**:
- 创建通知模板
- 设置默认通知规则
- 初始化通知配置
- 清理过期通知

---

### 49. getSystemInfo (获取系统信息)
**文件路径**: `cloudfunctions/getSystemInfo/index.js`
**功能描述**: 获取系统配置信息

**返回值**:
```javascript
{
  success: Boolean,
  data: {
    version: String,         // 系统版本
    maintenance: Boolean,    // 维护状态
    features: Object,        // 功能开关
    config: Object          // 系统配置
  }
}
```

**主要功能**:
- 系统版本管理
- 维护模式控制
- 功能开关配置
- 客户端配置下发

---

## 🔄 数据同步云函数

### 50. syncUserData (同步用户数据)
**文件路径**: `cloudfunctions/syncUserData/index.js`
**功能描述**: 同步用户数据到各个系统

**主要功能**:
- 用户数据同步
- 跨系统数据一致性
- 增量同步机制
- 冲突解决策略

---

## 📈 分析统计云函数

### 51. getAnalyticsData (获取分析数据)
**文件路径**: `cloudfunctions/getAnalyticsData/index.js`
**功能描述**: 获取业务分析数据

**输入参数**:
```javascript
{
  type: String,         // 分析类型
  timeRange: String,    // 时间范围
  dimensions: Array     // 分析维度
}
```

**返回值**:
```javascript
{
  success: Boolean,
  data: {
    metrics: Object,     // 指标数据
    trends: Array,       // 趋势数据
    insights: Array      // 洞察信息
  }
}
```

**主要功能**:
- 用户行为分析
- 订单趋势分析
- 收入分析
- 用户留存分析

---

## 🛠️ 维护工具云函数

### 52. dataBackup (数据备份)
**文件路径**: `cloudfunctions/dataBackup/index.js`
**功能描述**: 数据备份功能

**主要功能**:
- 定时数据备份
- 增量备份策略
- 备份文件管理
- 恢复功能支持

---

### 53. systemHealth (系统健康检查)
**文件路径**: `cloudfunctions/systemHealth/index.js`
**功能描述**: 系统健康状态检查

**返回值**:
```javascript
{
  success: Boolean,
  data: {
    database: String,     // 数据库状态
    storage: String,      // 存储状态
    functions: Object,    // 云函数状态
    overall: String       // 整体状态
  }
}
```

**主要功能**:
- 数据库连接检查
- 云存储状态检查
- 云函数运行状态
- 系统性能监控

---

## 📋 配置管理

### 环境配置
```javascript
// 云函数环境配置
cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV  // 动态环境
});
```

### 数据库集合
- **users**: 用户信息
- **orders**: 订单数据
- **chat_rooms**: 聊天室
- **chat_messages**: 聊天消息
- **notifications**: 通知记录
- **transactions**: 交易记录
- **evaluations**: 评价数据
- **announcements**: 系统公告
- **daily_passwords**: 每日密码
- **reputation_records**: 信誉记录

### 安全配置
- 所有云函数都进行openid验证
- 敏感操作需要额外权限检查
- 数据访问权限控制
- 请求频率限制

---

## 🚀 性能优化

### 缓存策略
- 公告列表缓存 (2分钟)
- 每日密码缓存 (24小时)
- 用户信息缓存 (5分钟)
- 统计数据缓存 (10分钟)

### 并发控制
- 抢单操作原子性保证
- 分布式锁机制
- 重试机制
- 超时处理

### 数据库优化
- 合理的索引设计
- 分页查询优化
- 聚合查询优化
- 定期数据清理

---

## 📞 技术支持

如有问题请联系开发团队：
- 邮箱: <EMAIL>
- 微信群: 三角洲技术支持群

---

**文档版本**: v1.0
**最后更新**: 2025-08-02
**维护者**: 三角洲开发团队
